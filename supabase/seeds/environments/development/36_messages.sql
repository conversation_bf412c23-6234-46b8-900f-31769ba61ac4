-- Start transaction
BEGIN;

-- Create messages between users
DO $BODY$
DECLARE
  conversation_record RECORD;
  message_count INTEGER;
  i INTEGER;
  sender_user_id UUID;
  
  message_contents TEXT[] := ARRAY[
    'Patient lab results are ready for review',
    'Can you check the medication dosage for this patient?',
    'Appointment needs to be rescheduled for tomorrow',
    'Patient is experiencing side effects from new medication',
    'Insurance authorization came through for the procedure',
    'Please review the discharge summary before patient leaves',
    'Lab values are outside normal range - follow up needed',
    'Patient called with questions about their treatment plan',
    'Referral has been approved by the specialist',
    'Urgent: Patient needs immediate attention'
  ];

BEGIN
  -- Create messages for existing conversations
  FOR conversation_record IN
    SELECT c.id as conversation_id
    FROM conversations c
    ORDER BY RANDOM()
    LIMIT 100  -- Create messages for 100 conversations
  LOOP
    -- Each conversation gets 2-8 messages
    message_count := 2 + floor(random() * 7)::int;
    
    FOR i IN 1..message_count LOOP
      -- Get a random participant from this conversation as sender
      SELECT cp.user_id INTO sender_user_id
      FROM conversation_participants cp
      WHERE cp.conversation_id = conversation_record.conversation_id
      ORDER BY RANDOM()
      LIMIT 1;
      
      INSERT INTO public.messages (
        id,
        conversation_id,
        sender_id,
        content,
        attachments,
        metadata,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        conversation_record.conversation_id,
        sender_user_id,
        message_contents[1 + (random() * (array_length(message_contents, 1) - 1))::int],
        '[]'::jsonb,
        jsonb_build_object('priority', 'normal', 'read', random() < 0.7),
        NOW() - make_interval(days => (random() * 30)::int, hours => (random() * 24)::int),
        NOW()
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT; 