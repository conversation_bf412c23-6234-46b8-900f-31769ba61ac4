import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Organization } from "@/contexts/auth-context-types";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { cacheOrganizationData } from "@/lib/auth/organization-cache";
import { EnhancedOrganization } from "@/lib/auth/organization-types";
import { supabase } from "@/lib/supabase";
import { Building2, ChevronDown, Edit } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

export function RoleBasedOrgSelector() {
  const { user, organization, setOrganization } = useAuth();
  const { isSystemAdmin, isAdmin } = useUserRoles();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [hasMultipleOrgs, setHasMultipleOrgs] = useState(false);

  // Fetch all organizations for system admins
  useEffect(() => {
    const fetchOrganizations = async () => {
      if (!user || !isSystemAdmin) return;

      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('organizations')
          .select('*')
          .order('name');

        if (error) {
          console.error('Error fetching all organizations:', error);
          return;
        }

        if (data && data.length > 0) {
          setOrganizations(data);
          setHasMultipleOrgs(data.length > 1);

          console.debug(`Loaded ${data.length} organizations for system admin`);
        }
      } catch (err) {
        console.error('Error fetching all organizations:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizations();
  }, [user, isSystemAdmin]);

  // Get organizations from the current organization if it's enhanced
  useEffect(() => {
    if (organization && !isSystemAdmin) {
      const enhancedOrg = organization as EnhancedOrganization;
      const hasMultiple = !!enhancedOrg.hasMultipleOrgs;
      setHasMultipleOrgs(hasMultiple);

      if (enhancedOrg.availableOrgs && enhancedOrg.availableOrgs.length > 0) {
        setOrganizations(enhancedOrg.availableOrgs);
        console.debug(`Loaded ${enhancedOrg.availableOrgs.length} organizations for user`);
      }
    }
  }, [organization?.id, isSystemAdmin]);

  const handleOrganizationSelect = async (org: Organization) => {
    if (!user || !org) return;

    try {
      setIsLoading(true);

      // Set the organization in the auth context
      setOrganization(org);

      // Cache the organization with isLastSelected=true
      cacheOrganizationData(user.id, org, { isLastSelected: true });

      // Also store in localStorage directly for redundancy
      try {
        localStorage.setItem('spritely_last_org', org.id);
        localStorage.setItem('spritely_last_org_name', org.name);
        localStorage.setItem('spritely_last_org_user', user.id);
      } catch (e) {
        console.warn('Error storing organization in localStorage:', e);
      }

      // Show success message and redirect
      setTimeout(() => {
        toast.success(`Switched to ${org.name}`);

        // Refresh the page to ensure all components update with the new organization
        window.location.href = '/dashboard';
      }, 100);
    } catch (err: unknown) {
      const error = err as { message?: string };
      console.error('Failed to switch organization:', err);
      toast.error(`Failed to switch organization: ${error.message || 'Unknown error'}`);
      setIsLoading(false);
    }
  };

  // Handle edit organization click
  const handleEditOrganization = (e: React.MouseEvent, orgId: string) => {
    e.stopPropagation(); // Prevent dropdown item click
    navigate(`/organizations/${orgId}/settings`);
  };

  // Always show the current organization, but only make it a dropdown if:
  // 1. User is an admin (system_admin or org_admin)
  // 2. User is a system admin (who can see all orgs) or has multiple organizations
  const canSwitchOrgs = isAdmin && (isSystemAdmin || hasMultipleOrgs);

  // If there's no organization at all, show a placeholder
  if (!organization && organizations.length === 0) {
    return (
      <Button variant="ghost" size="sm" className="flex items-center gap-2" disabled>
        <Building2 className="h-4 w-4" />
        <span className="max-w-[200px] truncate">No Organization</span>
      </Button>
    );
  }

  // If user can't switch orgs, just show the current org without dropdown
  if (!canSwitchOrgs) {
    return (
      <Button variant="ghost" size="sm" className="flex items-center gap-2" disabled={isLoading}>
        <Building2 className="h-4 w-4" />
        <span className="max-w-[200px] truncate font-medium">
          {organization?.name || "Select Organization"}
        </span>
      </Button>
    );
  }

  // Otherwise, show the dropdown with all available organizations
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={isLoading}>
        <Button variant="ghost" size="sm" className="flex items-center gap-2 min-w-[200px]">
          <Building2 className="h-5 w-5 text-primary" />
          <span className="max-w-[150px] truncate font-medium">
            {organization?.name || "Select Organization"}
          </span>
          <ChevronDown className="h-4 w-4 opacity-50 ml-auto" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="center" className="w-[250px]">
        <DropdownMenuLabel>Switch Organization</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {organizations.map((org: Organization) => {
          const isCurrent = organization && organization.id === org.id;
          return (
            <DropdownMenuItem
              key={org.id}
              onClick={() => handleOrganizationSelect(org)}
              disabled={isLoading || isCurrent}
              className="flex items-center justify-between"
            >
              <div className="flex items-center flex-1">
                <span className="truncate">{org.name}</span>
                {isCurrent && (
                  <span className="ml-2 text-xs text-primary font-medium">(Current)</span>
                )}
              </div>
              {isSystemAdmin && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 ml-2 opacity-60 hover:opacity-100"
                  onClick={(e) => handleEditOrganization(e, org.id)}
                >
                  <Edit className="h-3 w-3" />
                </Button>
              )}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
