import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { Organization as ContextOrganization } from "@/contexts/auth-context-types";
import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/lib/supabase";
import { Building2, ChevronDown, Edit, Settings } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Define simplified types for what we actually need
interface Organization {
  id: string;
  name: string;
  type: string;
  settings: Record<string, unknown>;
}

interface UserRoleWithOrganization {
  role: string;
  invitation_status: string | null;
  organization: Organization;
}


export function OrganizationSwitcher() {
  const { user, organization: currentOrg, setOrganization } = useAuth();
  const navigate = useNavigate();
  const [userRoles, setUserRoles] = useState<UserRoleWithOrganization[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchUserRoles = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('user_roles')
        .select(`
          role,
          invitation_status,
          organization:organizations (
            id,
            name,
            type,
            settings,
            owner_id
          )
        `)
        .eq('user_id', user.id);

      if (error) throw error;

      // Cast the data to handle the structure mismatch
      const rawData = data as unknown as Array<{
        role: string;
        invitation_status: string | null;
        organization: {
          id: string;
          name: string;
          type: string;
          settings: Record<string, unknown>;
        }
      }>;

      // Transform the data to match our expected structure
      const transformedData: UserRoleWithOrganization[] = rawData.map(item => ({
        role: item.role,
        invitation_status: item.invitation_status,
        organization: {
          id: item.organization.id,
          name: item.organization.name,
          type: item.organization.type,
          settings: item.organization.settings || {}
        }
      }));

      setUserRoles(transformedData);
    } catch (error) {
      console.error('Error fetching user roles:', error);
      toast.error('Failed to load organizations');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchUserRoles();
  }, [fetchUserRoles]);

  const handleOrganizationSelect = (org: Organization) => {
    try {
      // Convert our simplified Organization to the full type expected by setOrganization
      const fullOrg = {
        id: org.id,
        name: org.name,
        type: org.type,
        settings: org.settings,
        // Add missing properties with default values
        billing_info: {},
        created_at: new Date().toISOString(),
        subscription_tier: 'free',
        updated_at: new Date().toISOString()
      };

      // Cast to the expected type
      setOrganization(fullOrg as unknown as ContextOrganization);
      toast.success(`Switched to ${org.name}`);
      navigate('/dashboard');
    } catch (error) {
      console.error('Error switching organization:', error);
      toast.error('Failed to switch organization');
    }
  };

  if (isLoading) {
    return (
      <Skeleton className="h-9 w-[200px]" />
    );
  }

  if (!currentOrg || userRoles.length === 0) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="gap-2"
        onClick={() => navigate('/setup')}
      >
        <Building2 className="h-4 w-4" />
        <span>Select Organization</span>
      </Button>
    );
  }

  const isAdmin = userRoles.some(
    role => role.organization.id === currentOrg?.id &&
    ['system_admin', 'org_admin'].includes(role.role)
  );

  // Function to check if user can edit a specific organization
  const canEditOrganization = (orgId: string) => {
    return userRoles.some(
      role => role.organization.id === orgId &&
      ['system_admin', 'org_admin'].includes(role.role)
    );
  };

  // Handle edit organization click
  const handleEditOrganization = (e: React.MouseEvent, orgId: string) => {
    e.stopPropagation(); // Prevent dropdown item click
    navigate(`/organizations/${orgId}/settings`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Building2 className="h-4 w-4" />
          <span className="max-w-[150px] truncate">{currentOrg.name}</span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[220px]">
        <DropdownMenuLabel>Organizations</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {userRoles.map(({ organization: org }) => (
          <DropdownMenuItem
            key={org.id}
            className="cursor-pointer flex items-center justify-between"
            onClick={() => handleOrganizationSelect(org)}
          >
            <div className="flex items-center flex-1">
              <span>{org.name}</span>
              {currentOrg.id === org.id && (
                <span className="ml-2 text-xs text-muted-foreground">(Current)</span>
              )}
            </div>
            {canEditOrganization(org.id) && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 ml-2 opacity-60 hover:opacity-100"
                onClick={(e) => handleEditOrganization(e, org.id)}
              >
                <Edit className="h-3 w-3" />
              </Button>
            )}
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="cursor-pointer"
          onClick={() => navigate('/organizations')}
        >
          Manage Organizations
        </DropdownMenuItem>
        {isAdmin && (
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => navigate(`/organizations/${currentOrg.id}/settings`)}
          >
            <Settings className="mr-2 h-4 w-4" />
            Organization Settings
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}