import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Organization as ContextOrganization } from "@/contexts/auth-context-types";
import { useAuth } from "@/hooks/useAuth";
import { canManageOrganization, isSystemAdmin } from "@/lib/permissions/organization-permissions";
import { supabase } from "@/lib/supabase";
import { Building2, ChevronDown, Edit, Search, Settings, Table } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Define simplified types for what we actually need
interface Organization {
  id: string;
  name: string;
  type: string;
  settings: Record<string, unknown>;
}

interface UserRoleWithOrganization {
  role: string;
  invitation_status: string | null;
  custom_permissions: Record<string, unknown>;
  organization: Organization;
}


export function OrganizationSwitcher() {
  const { user, organization: currentOrg, setOrganization } = useAuth();
  const navigate = useNavigate();
  const [userRoles, setUserRoles] = useState<UserRoleWithOrganization[]>([]);
  const [filteredRoles, setFilteredRoles] = useState<UserRoleWithOrganization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  const fetchUserRoles = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('user_roles')
        .select(`
          role,
          invitation_status,
          custom_permissions,
          organization:organizations (
            id,
            name,
            type,
            settings,
            owner_id
          )
        `)
        .eq('user_id', user.id);

      if (error) throw error;

      // Cast the data to handle the structure mismatch
      const rawData = data as unknown as Array<{
        role: string;
        invitation_status: string | null;
        custom_permissions: Record<string, unknown>;
        organization: {
          id: string;
          name: string;
          type: string;
          settings: Record<string, unknown>;
          owner_id: string;
        }
      }>;

      // Transform the data to match our expected structure
      const transformedData: UserRoleWithOrganization[] = rawData.map(item => ({
        role: item.role,
        invitation_status: item.invitation_status,
        custom_permissions: item.custom_permissions || {},
        organization: {
          id: item.organization.id,
          name: item.organization.name,
          type: item.organization.type,
          settings: item.organization.settings || {}
        }
      }));

      setUserRoles(transformedData);
      setFilteredRoles(transformedData);
    } catch (error) {
      console.error('Error fetching user roles:', error);
      toast.error('Failed to load organizations');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchUserRoles();
  }, [fetchUserRoles]);

  // Filter organizations based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredRoles(userRoles);
    } else {
      const filtered = userRoles.filter(role =>
        role.organization.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        role.organization.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredRoles(filtered);
    }
  }, [searchTerm, userRoles]);

  const handleOrganizationSelect = (org: Organization) => {
    try {
      // Clear search term
      setSearchTerm("");

      // Convert our simplified Organization to the full type expected by setOrganization
      const fullOrg = {
        id: org.id,
        name: org.name,
        type: org.type,
        settings: org.settings,
        // Add missing properties with default values
        billing_info: {},
        created_at: new Date().toISOString(),
        subscription_tier: 'free',
        updated_at: new Date().toISOString()
      };

      // Cast to the expected type
      setOrganization(fullOrg as unknown as ContextOrganization);
      toast.success(`Switched to ${org.name}`);
      navigate('/dashboard');
    } catch (error) {
      console.error('Error switching organization:', error);
      toast.error('Failed to switch organization');
    }
  };

  if (isLoading) {
    return (
      <Skeleton className="h-9 w-[200px]" />
    );
  }

  if (!currentOrg || userRoles.length === 0) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="gap-2"
        onClick={() => navigate('/setup')}
      >
        <Building2 className="h-4 w-4" />
        <span>Select Organization</span>
      </Button>
    );
  }

  const isAdmin = userRoles.some(
    role => role.organization.id === currentOrg?.id &&
    ['system_admin', 'org_admin'].includes(role.role)
  );

  // Function to check if user can edit a specific organization
  const canEditOrganization = (orgId: string) => {
    const roleData = userRoles.map(role => ({
      role: role.role,
      custom_permissions: role.custom_permissions,
      organization_id: role.organization.id
    }));

    return canManageOrganization(roleData, orgId, user?.id);
  };

  // Check if user is a system admin (can manage all organizations)
  const userIsSystemAdmin = isSystemAdmin(userRoles.map(role => ({ role: role.role })));

  // Handle edit organization click
  const handleEditOrganization = (e: React.MouseEvent, orgId: string) => {
    e.stopPropagation(); // Prevent dropdown item click
    navigate(`/organizations/${orgId}/settings`);
  };

  return (
    <DropdownMenu onOpenChange={(open) => !open && setSearchTerm("")}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Building2 className="h-4 w-4" />
          <span className="max-w-[150px] truncate">{currentOrg.name}</span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[320px]">
        <DropdownMenuLabel>Manage Organizations</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Search Input */}
        <div className="px-2 py-1" onClick={(e) => e.stopPropagation()}>
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search organizations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 h-8"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
        <DropdownMenuSeparator />

        {/* Organizations List */}
        <div className="max-h-[300px] overflow-y-auto">
          {filteredRoles.map(({ organization: org }) => (
          <DropdownMenuItem
            key={org.id}
            className="cursor-pointer flex items-center justify-between"
            onClick={() => handleOrganizationSelect(org)}
          >
            <div className="flex items-center flex-1">
              <span>{org.name}</span>
              {currentOrg.id === org.id && (
                <span className="ml-2 text-xs text-muted-foreground">(Current)</span>
              )}
            </div>
            {canEditOrganization(org.id) && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 ml-2 opacity-60 hover:opacity-100"
                onClick={(e) => handleEditOrganization(e, org.id)}
              >
                <Edit className="h-3 w-3" />
              </Button>
            )}
          </DropdownMenuItem>
        ))}

        {filteredRoles.length === 0 && searchTerm && (
          <div className="px-2 py-4 text-center text-sm text-muted-foreground">
            No organizations found matching "{searchTerm}"
          </div>
        )}
        </div>

        <DropdownMenuSeparator />

        {/* Management Options */}
        {userIsSystemAdmin && (
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => navigate('/organizations/manage')}
          >
            <Table className="mr-2 h-4 w-4" />
            View All Organizations
          </DropdownMenuItem>
        )}
        {isAdmin && (
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => navigate(`/organizations/${currentOrg.id}/settings`)}
          >
            <Settings className="mr-2 h-4 w-4" />
            Organization Settings
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}